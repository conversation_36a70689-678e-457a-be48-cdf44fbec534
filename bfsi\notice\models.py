import json

from django.db import models
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel

from authorization.models import User
# Removed direct import of Profile to avoid circular import


class Company(TimeStampedModel):
    name = models.CharField(max_length=300)
    json_data = models.TextField(null=True, blank=True, default='{}')

    def get_json_field(self):
        "Returns json object"
        return json.loads(self.json_data)

    def set_json_field(self, data):
        "Saves json object"
        self.json_data = json.dumps(data)

    # access additional data by this variable
    json_field = property(get_json_field, set_json_field)

    def __str__(self):
        return self.name


class Entry(models.Model):
    STATUS_CHOICES = (
        ('initiated', 'Initiated'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed'),
    )

    msg_id = models.CharField(max_length=100)
    phone_number = models.Char<PERSON>ield(max_length=15)
    loan_id = models.CharField(max_length=100, null=True, blank=True)
    dispute = models.ForeignKey('odr.Dispute', on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    failed_reason = models.CharField(
        max_length=100, null=True, blank=True, default='')
    timestamp = models.DateTimeField(default=timezone.now)  # Changed to use timezone.now
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    borrower_type = models.CharField(
        max_length=50, null=True, blank=True, default='primary')
    template = models.ForeignKey(
        'Template', on_delete=models.CASCADE, null=True, blank=True)
    notice = models.ForeignKey(
        Notice, on_delete=models.CASCADE, null=True, blank=True, related_name='entries')

    def save(self, *args, **kwargs):
        # Ensure timestamp is timezone-aware UTC
        if self.timestamp and timezone.is_naive(self.timestamp):
            self.timestamp = timezone.make_aware(self.timestamp, timezone.utc)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Recipient: {self.phone_number}, Status: {self.status}"


class Campaign(models.Model):
    """Model to track campaigns."""
    client = models.ForeignKey('odr.Profile', on_delete=models.CASCADE, null=True, blank=True)
    excel_file_name = models.CharField(max_length=255, null=True, blank=True)
    excel_file_uploaded = models.BooleanField(default=False)
    total_rows = models.IntegerField(default=0)
    number_of_cases_created = models.IntegerField(default=0)
    arbitrators_assigned = models.BooleanField(default=False, blank=True, null=True)
    assigned_arbitrators_at = models.DateTimeField(null=True, blank=True)
    case_managers_assigned = models.BooleanField(default=False, blank=True, null=True)
    assigned_case_managers_at = models.DateTimeField(null=True, blank=True)
    processing_errors = models.JSONField(default=list, blank=True)
    processed_rows = models.IntegerField(default=0, null=True, blank=True)
    case_creation_status = models.CharField(max_length=50, null=True, blank=True)
    case_creation_report_generated = models.BooleanField(default=False)
    case_creation_report_file_path = models.CharField(max_length=255, null=True, blank=True)
    flow_type = models.CharField(max_length=50, null=True, blank=True)
    has_co_borrowers = models.BooleanField(default=False, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)  # Changed from auto_now_add
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True)
    master_excel_generated = models.BooleanField(default=False, null=True, blank=True)
    master_excel_generation = models.CharField(
        max_length=20, choices=[('processing', 'Processing'), ('completed', 'Completed')], default='pending')
    def save(self, *args, **kwargs):
        # Ensure all datetime fields are timezone-aware UTC
        if self.assigned_arbitrators_at and timezone.is_naive(self.assigned_arbitrators_at):
            self.assigned_arbitrators_at = timezone.make_aware(self.assigned_arbitrators_at, timezone.utc)
        if self.assigned_case_managers_at and timezone.is_naive(self.assigned_case_managers_at):
            self.assigned_case_managers_at = timezone.make_aware(self.assigned_case_managers_at, timezone.utc)
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)

    def get_progress(self):
        return {
            'total': self.total_rows,
            'processed': self.processed_rows,
            'success': self.number_of_cases_created,
            'errors': len(self.processing_errors)
        }

    class Meta:
        ordering = ['-created_at']


class WhatsAppTemplate(models.Model):
    """Model to store WhatsApp templates."""
    # template = models.OneToOneField(Template, on_delete=models.CASCADE, related_name='whatsapp_template', null=True, blank=True)
    whatsapp_template_id = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=255)
    body = models.TextField(help_text="Full body of the template")
    lang_code = models.CharField(max_length=10)
    requires_attachment = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # WhatsApp sending status fields (moved from Campaign)
    sent_to_whatsapp = models.BooleanField(default=False)
    whatsapp_processed_rows = models.IntegerField(default=0, null=True, blank=True)
    whatsapp_messages_sent = models.IntegerField(default=0, null=True, blank=True)
    whatsapp_status = models.CharField(max_length=50, null=True, blank=True)
    whatsapp_errors = models.JSONField(default=list, null=True, blank=True)

    is_s21_notice = models.BooleanField(default=False)
    is_s138_notice = models.BooleanField(default=False)

    # conciliation notice fields
    parameter_mapping = models.JSONField(default=dict, null=True, blank=True)
    is_termination_notice = models.BooleanField(default=False)
    is_payment_request_notice = models.BooleanField(default=False, null=True, blank=True)
    has_body_params = models.BooleanField(default=False, null=True, blank=True)
    conciliation_notice_1 = models.BooleanField(default=False, null=True, blank=True)
    conciliation_notice_2 = models.BooleanField(default=False, null=True, blank=True)
    conciliation_notice_3 = models.BooleanField(default=False, null=True, blank=True)
    conciliation_notice_4 = models.BooleanField(default=False, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)  # Changed from auto_now_add
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Ensure created_at is timezone-aware UTC
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.whatsapp_template_id})"


class EmailTemplate(models.Model):
    """Model to store Email templates."""
    # template = models.OneToOneField(Template, on_delete=models.CASCADE, related_name='email_template', null=True, blank=True)
    email_template_id = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=255)
    subject = models.CharField(max_length=255, null=True, blank=True)
    body = models.TextField(help_text="Full body of the template")
    requires_attachment = models.BooleanField(default=False)

    # Email sending status fields
    sent_to_email = models.BooleanField(default=False)
    email_processed_rows = models.IntegerField(default=0, null=True, blank=True)
    email_messages_sent = models.IntegerField(default=0, null=True, blank=True)
    email_status = models.CharField(max_length=50, null=True, blank=True)
    email_errors = models.JSONField(default=list, null=True, blank=True)
    co_borrower_emails_sent = models.IntegerField(default=0, null=True, blank=True)

    is_s21_notice = models.BooleanField(default=False)
    is_s138_notice = models.BooleanField(default=False)

    # conciliation notice fields
    parameter_mapping = models.JSONField(default=dict, null=True, blank=True)
    is_termination_notice = models.BooleanField(default=False)
    is_payment_request_notice = models.BooleanField(default=False, null=True, blank=True)
    conciliation_notice_1 = models.BooleanField(default=False, null=True, blank=True)
    # we don't send conciliation notice 2 and 3 via email, so they are not included
    conciliation_notice_4 = models.BooleanField(default=False, null=True, blank=True)
    conciliation_email_reminder = models.BooleanField(default=False, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)  # Changed from auto_now_add
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Ensure created_at is timezone-aware UTC
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.email_template_id})"


class Template(models.Model):
    """Parent model for templates."""
    template_id = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    # campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='templates', null=True, blank=True)
    profile = models.ForeignKey('odr.Profile', on_delete=models.RESTRICT, null=True, blank=True, related_name='templates')

    # Template processing status fields (moved from Campaign)
    pdf_file_name = models.CharField(max_length=255, null=True, blank=True)
    pdf_file_uploaded = models.BooleanField(default=False)
    renaming_and_splitting_done = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)  # Changed from auto_now_add
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True)

    whatsapp_template = models.OneToOneField(WhatsAppTemplate, on_delete=models.SET_NULL, related_name='parent_template', null=True, blank=True)
    email_template = models.OneToOneField(EmailTemplate, on_delete=models.SET_NULL, related_name='parent_template', null=True, blank=True)

    total_splits_expected = models.IntegerField(default=0, null=True, blank=True)
    splits_processed = models.IntegerField(default=0, null=True, blank=True)
    splitting_status = models.CharField(max_length=50, default='pending', null=True, blank=True)
    splitting_errors = models.JSONField(default=list, null=True, blank=True)

    file_section_template = models.BooleanField(default=False, null=True, blank=True, help_text="Indicates if this template is used in file section")

    def save(self, *args, **kwargs):
        # Ensure created_at is timezone-aware UTC
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.template_id})"


class Notice(models.Model):
    dispute = models.ForeignKey('odr.Dispute', on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.SET_NULL, null=True, blank=True, related_name='notices')
    file_path = models.CharField(max_length=1000, null=True, blank=True)
    file_name = models.CharField(max_length=1000, null=True, blank=True)
    notice_type = models.CharField(max_length=1000, null=True, blank=True)
    size = models.CharField(max_length=1000, null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.RESTRICT, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now, null=True, blank=True)  # Changed from auto_now_add

    def save(self, *args, **kwargs):
        # Ensure created_at is timezone-aware UTC
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)


class CampaignTemplate(models.Model):
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.CASCADE)
    created_at = models.DateTimeField(default=timezone.now)  # Changed from auto_now_add
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Ensure created_at is timezone-aware UTC
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(self.created_at, timezone.utc)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.campaign} - {self.template}"
